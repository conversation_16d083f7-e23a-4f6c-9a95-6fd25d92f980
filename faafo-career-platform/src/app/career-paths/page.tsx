'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { Briefcase, ArrowRight, CheckCircle, Circle } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface CareerPath {
  id: string;
  name: string;
  slug: string;
  overview: string;
  pros: string[];
  cons: string[];
  actionableSteps: string[];
  isActive: boolean;
}

export default function CareerPathsPage() {
  const { status } = useSession();
  const [careerPaths, setCareerPaths] = useState<CareerPath[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchCareerPaths();
  }, []);

  const fetchCareerPaths = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/career-paths');
      
      if (!response.ok) {
        throw new Error('Failed to fetch career paths');
      }

      const data = await response.json();
      setCareerPaths(data.careerPaths || []);
    } catch (err) {
      console.error('Error fetching career paths:', err);
      setError('Failed to load career paths');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">Loading career paths...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">Explore Career Paths</h1>
        <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Discover alternative career paths that could be your next step towards a more fulfilling professional life.
        </p>
      </div>

      {error && (
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-8">
          {error}
        </div>
      )}

      {careerPaths.length === 0 ? (
        <div className="text-center py-12">
          <Briefcase className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-xl font-medium text-gray-900 dark:text-gray-100 mb-2">
            No career paths available
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Career paths will be available soon. Check back later!
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {careerPaths.map((path) => (
            <div
              key={path.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Briefcase className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {path.name}
                  </h2>
                </div>
              </div>

              <p className="text-gray-700 dark:text-gray-300 mb-6 leading-relaxed">
                {path.overview}
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                  <h3 className="font-semibold text-green-700 dark:text-green-400 mb-2 flex items-center gap-1">
                    <CheckCircle className="h-4 w-4" />
                    Pros
                  </h3>
                  <ul className="space-y-1">
                    {path.pros.slice(0, 3).map((pro, index) => (
                      <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-1">
                        <Circle className="h-2 w-2 mt-2 text-green-500 fill-current" />
                        {pro}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h3 className="font-semibold text-red-700 dark:text-red-400 mb-2 flex items-center gap-1">
                    <Circle className="h-4 w-4" />
                    Cons
                  </h3>
                  <ul className="space-y-1">
                    {path.cons.slice(0, 3).map((con, index) => (
                      <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-1">
                        <Circle className="h-2 w-2 mt-2 text-red-500 fill-current" />
                        {con}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2">
                  Getting Started
                </h3>
                <ul className="space-y-1">
                  {path.actionableSteps.slice(0, 3).map((step, index) => (
                    <li key={index} className="text-sm text-gray-600 dark:text-gray-400 flex items-start gap-2">
                      <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded-full w-5 h-5 flex items-center justify-center text-xs font-medium mt-0.5">
                        {index + 1}
                      </span>
                      {step}
                    </li>
                  ))}
                  {path.actionableSteps.length > 3 && (
                    <li className="text-sm text-gray-500 dark:text-gray-500 italic">
                      +{path.actionableSteps.length - 3} more steps...
                    </li>
                  )}
                </ul>
              </div>

              <div className="flex gap-3">
                <Button asChild className="flex-1">
                  <Link href={`/career-paths/${path.id}`} className="flex items-center justify-center gap-2">
                    Learn More
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
                {status === 'authenticated' && (
                  <Button variant="outline" size="sm">
                    Bookmark
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {status === 'unauthenticated' && (
        <div className="mt-12 text-center bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
            Get Personalized Recommendations
          </h3>
          <p className="text-blue-700 dark:text-blue-300 mb-4">
            Sign up to take our assessment and get career path suggestions tailored to your goals and situation.
          </p>
          <Button asChild>
            <Link href="/signup">Get Started</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
